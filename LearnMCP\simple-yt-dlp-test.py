#!/usr/bin/env python3

"""
Simple yt-dlp test using direct executable
"""

import subprocess
import json
import sys
from pathlib import Path

def test_video_info(url):
    """Test getting video information"""
    
    print("📊 TESTING VIDEO INFO EXTRACTION")
    print("=" * 40)
    
    try:
        # Use the yt-dlp executable from venv
        yt_dlp_path = Path("../.venv/Scripts/yt-dlp.exe")
        
        cmd = [
            str(yt_dlp_path),
            '--dump-json',
            '--no-download',
            url
        ]
        
        print(f"🔄 Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            video_info = json.loads(result.stdout)
            
            print("✅ SUCCESS! Video information extracted:")
            print(f"  📝 Title: {video_info.get('title', 'N/A')}")
            print(f"  👤 Uploader: {video_info.get('uploader', 'N/A')}")
            print(f"  ⏱️  Duration: {video_info.get('duration', 0)} seconds ({video_info.get('duration', 0) // 60}:{video_info.get('duration', 0) % 60:02d})")
            print(f"  👀 Views: {video_info.get('view_count', 0):,}")
            print(f"  👍 Likes: {video_info.get('like_count', 0):,}")
            print(f"  📅 Upload Date: {video_info.get('upload_date', 'N/A')}")
            print(f"  📄 Description: {video_info.get('description', 'N/A')[:100]}...")
            
            # Check audio formats
            formats = video_info.get('formats', [])
            audio_formats = [f for f in formats if f.get('acodec') != 'none']
            print(f"  🎵 Audio formats available: {len(audio_formats)}")
            
            # Check subtitles
            subtitles = video_info.get('subtitles', {})
            auto_captions = video_info.get('automatic_captions', {})
            
            print(f"  📝 Manual subtitles: {list(subtitles.keys()) if subtitles else 'None'}")
            print(f"  🤖 Auto captions: {list(auto_captions.keys()) if auto_captions else 'None'}")
            
            return True, video_info
            
        else:
            print(f"❌ Failed: {result.stderr}")
            return False, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, None

def test_audio_extraction(url, duration_limit=30):
    """Test audio extraction with time limit"""
    
    print(f"\n🎵 TESTING AUDIO EXTRACTION ({duration_limit}s sample)")
    print("=" * 40)
    
    try:
        # Create temp directory
        temp_dir = Path("temp_test")
        temp_dir.mkdir(exist_ok=True)
        
        yt_dlp_path = Path("../.venv/Scripts/yt-dlp.exe")
        
        cmd = [
            str(yt_dlp_path),
            '-x',  # Extract audio only
            '--audio-format', 'mp3',
            '--audio-quality', '5',  # Medium quality
            '--download-sections', f'*0-{duration_limit}',  # First N seconds
            '-o', str(temp_dir / 'test_audio.%(ext)s'),
            url
        ]
        
        print(f"🔄 Extracting first {duration_limit} seconds...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        # Check for created files
        audio_files = list(temp_dir.glob("*.mp3"))
        
        if audio_files:
            audio_file = audio_files[0]
            file_size = audio_file.stat().st_size
            
            print("✅ SUCCESS! Audio extracted:")
            print(f"  📁 File: {audio_file.name}")
            print(f"  📏 Size: {file_size:,} bytes ({file_size / 1024 / 1024:.1f} MB)")
            
            # Clean up
            audio_file.unlink()
            temp_dir.rmdir()
            
            return True
        else:
            print("❌ No audio file created")
            print(f"Command output: {result.stdout}")
            print(f"Command error: {result.stderr}")
            
            # Clean up
            try:
                temp_dir.rmdir()
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_subtitle_extraction(url):
    """Test subtitle extraction"""
    
    print(f"\n📝 TESTING SUBTITLE EXTRACTION")
    print("=" * 40)
    
    try:
        temp_dir = Path("temp_subs")
        temp_dir.mkdir(exist_ok=True)
        
        yt_dlp_path = Path("../.venv/Scripts/yt-dlp.exe")
        
        cmd = [
            str(yt_dlp_path),
            '--skip-download',
            '--write-subs',
            '--write-auto-subs',
            '--sub-langs', 'en',
            '--sub-format', 'vtt',
            '-o', str(temp_dir / '%(title)s.%(ext)s'),
            url
        ]
        
        print("🔄 Attempting subtitle extraction...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        # Check for subtitle files
        sub_files = list(temp_dir.glob("*.vtt"))
        
        if sub_files:
            print("✅ SUCCESS! Subtitles found:")
            for sub_file in sub_files:
                file_size = sub_file.stat().st_size
                print(f"  📁 File: {sub_file.name}")
                print(f"  📏 Size: {file_size:,} bytes")
                
                # Read sample content
                with open(sub_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')[:15]  # First 15 lines
                    print(f"  📄 Sample content:")
                    for line in lines:
                        if line.strip():
                            print(f"    {line}")
                
                # Clean up
                sub_file.unlink()
            
            temp_dir.rmdir()
            return True
        else:
            print("❌ No subtitle files found")
            print(f"Output: {result.stdout}")
            print(f"Error: {result.stderr}")
            
            try:
                temp_dir.rmdir()
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    
    test_url = "https://www.youtube.com/watch?v=tmeCWULSTHc"
    if len(sys.argv) > 1:
        test_url = sys.argv[1]
    
    print("🚀 YT-DLP COMPREHENSIVE TEST")
    print("=" * 50)
    print(f"Testing URL: {test_url}")
    print()
    
    # Test 1: Video info
    info_success, video_info = test_video_info(test_url)
    
    # Test 2: Audio extraction (30 second sample)
    audio_success = test_audio_extraction(test_url, 30)
    
    # Test 3: Subtitle extraction
    subtitle_success = test_subtitle_extraction(test_url)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FINAL TEST RESULTS")
    print("=" * 50)
    print(f"✅ Video Info: {'SUCCESS' if info_success else 'FAILED'}")
    print(f"🎵 Audio Extract: {'SUCCESS' if audio_success else 'FAILED'}")
    print(f"📝 Subtitles: {'SUCCESS' if subtitle_success else 'FAILED'}")
    
    if info_success and audio_success:
        print("\n🎯 EXCELLENT! yt-dlp is working perfectly!")
        print("✅ Ready to extract full audio for Whisper transcription")
        
        if subtitle_success:
            print("✅ Bonus: Subtitles are available - no Whisper needed!")
        else:
            print("💡 No subtitles - Whisper API will be needed for transcription")
            
        if video_info:
            duration = video_info.get('duration', 0)
            cost_estimate = duration * 0.006 / 60  # $0.006 per minute
            print(f"💰 Estimated Whisper cost: ${cost_estimate:.2f} for full video")
            
    else:
        print("\n❌ Some tests failed - need troubleshooting")
        
    print("\n🚀 Next step: Build full extraction pipeline!")

if __name__ == "__main__":
    main()
