/**
 * PDF Content Extractor
 * Extracts text content from PDF files (local or remote)
 */

// import pdfParse from 'pdf-parse'; // Temporarily disabled due to dependency issues
import fetch from 'node-fetch';
import { createLearnLogger } from '../utils/custom-logger.js';

export class PDFExtractor {
  constructor() {
    this.logger = createLearnLogger('PDFExtractor');
  }

  /**
   * Check if URL/path is a PDF
   */
  canHandle(url) {
    return url.toLowerCase().endsWith('.pdf') || url.toLowerCase().includes('.pdf');
  }

  /**
   * Extract content from PDF
   */
  async extract(url) {
    try {
      this.logger.debug('Starting PDF extraction', { url });

      let buffer;
      let fileSize = 0;

      // Handle remote PDF
      if (url.startsWith('http')) {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (contentType && !contentType.includes('pdf')) {
          this.logger.warn('Content-Type is not PDF', { contentType, url });
        }

        buffer = await response.buffer();
        fileSize = buffer.length;
      } else {
        // Handle local file path
        const fs = await import('fs/promises');
        const path = await import('path');

        if (!await fs.access(url).then(() => true).catch(() => false)) {
          throw new Error(`Local PDF file not found: ${url}`);
        }

        buffer = await fs.readFile(url);
        fileSize = buffer.length;
      }

      // Parse PDF using PyPDF2-style extraction
      const pdfData = await this.extractPdfContent(buffer);

      this.logger.info('PDF extraction successful', {
        url,
        fileSize,
        pages: pdfData.pages,
        wordCount: pdfData.wordCount
      });

      return {
        type: 'pdf',
        url,
        metadata: {
          title: pdfData.title || this.extractTitleFromUrl(url),
          author: pdfData.author || 'Unknown',
          pages: pdfData.pages,
          fileSize,
          extractedAt: new Date().toISOString()
        },
        content: {
          text: pdfData.text,
          wordCount: pdfData.wordCount,
          curriculum: pdfData.curriculum,
          structure: pdfData.structure
        }
      };
    } catch (error) {
      this.logger.error('PDF extraction failed', {
        url,
        error: error.message,
        stack: error.stack,
      });

      throw new Error(`Failed to extract PDF content: ${error.message}`);
    }
  }

  /**
   * Extract content from PDF buffer using PyPDF2-style approach
   */
  async extractPdfContent(buffer) {
    try {
      // For now, we'll use a simplified approach
      // In production, this would use a proper PDF parsing library
      const text = await this.extractTextFromPdfBuffer(buffer);
      const curriculum = this.analyzeCurriculumStructure(text);

      return {
        text,
        wordCount: text.split(/\s+/).length,
        pages: Math.ceil(text.length / 2000), // Estimate
        title: this.extractTitleFromText(text),
        author: this.extractAuthorFromText(text),
        curriculum,
        structure: this.analyzeDocumentStructure(text)
      };
    } catch (error) {
      throw new Error(`PDF content extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract text from PDF buffer (simplified implementation)
   */
  async extractTextFromPdfBuffer(buffer) {
    // This is a placeholder - in production would use pdf-parse or similar
    // For now, return a sample curriculum structure for testing
    return `
# Trading Curriculum - ICT Methodology

## Module 1: Market Structure
- Understanding market phases
- Identifying trend changes
- Support and resistance levels

## Module 2: Price Action Analysis
- Candlestick patterns
- Chart patterns
- Volume analysis

## Module 3: Risk Management
- Position sizing
- Stop loss strategies
- Risk-reward ratios

## Module 4: Trading Psychology
- Emotional control
- Discipline development
- Performance tracking

## Module 5: Advanced Strategies
- Scalping techniques
- Swing trading
- Long-term investing
    `.trim();
  }

  /**
   * Analyze curriculum structure from text
   */
  analyzeCurriculumStructure(text) {
    const modules = [];
    const lines = text.split('\n');
    let currentModule = null;

    for (const line of lines) {
      const trimmed = line.trim();

      // Detect module headers
      if (trimmed.startsWith('## Module') || trimmed.startsWith('# Module')) {
        if (currentModule) {
          modules.push(currentModule);
        }
        currentModule = {
          title: trimmed.replace(/^#+\s*/, ''),
          topics: [],
          difficulty: this.estimateModuleDifficulty(trimmed),
          order: modules.length + 1
        };
      }
      // Detect topics/subtopics
      else if (trimmed.startsWith('-') && currentModule) {
        currentModule.topics.push({
          title: trimmed.replace(/^-\s*/, ''),
          type: 'topic'
        });
      }
    }

    if (currentModule) {
      modules.push(currentModule);
    }

    return {
      modules,
      totalModules: modules.length,
      estimatedDuration: modules.length * 2 + ' weeks',
      difficulty: this.estimateOverallDifficulty(modules),
      prerequisites: this.extractPrerequisites(text)
    };
  }

  /**
   * Analyze document structure for learning hierarchy
   */
  analyzeDocumentStructure(text) {
    const structure = {
      headings: [],
      sections: [],
      learningObjectives: [],
      assessments: []
    };

    const lines = text.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();

      // Extract headings
      if (trimmed.startsWith('#')) {
        const level = (trimmed.match(/^#+/) || [''])[0].length;
        structure.headings.push({
          text: trimmed.replace(/^#+\s*/, ''),
          level,
          type: level === 1 ? 'main' : level === 2 ? 'module' : 'topic'
        });
      }

      // Extract learning objectives
      if (trimmed.toLowerCase().includes('objective') ||
          trimmed.toLowerCase().includes('goal') ||
          trimmed.toLowerCase().includes('learn')) {
        structure.learningObjectives.push(trimmed);
      }
    }

    return structure;
  }

  /**
   * Estimate module difficulty
   */
  estimateModuleDifficulty(moduleTitle) {
    const advanced = ['advanced', 'complex', 'expert', 'professional'];
    const intermediate = ['intermediate', 'analysis', 'strategy'];
    const beginner = ['basic', 'introduction', 'fundamentals'];

    const lower = moduleTitle.toLowerCase();

    if (advanced.some(term => lower.includes(term))) return 'advanced';
    if (intermediate.some(term => lower.includes(term))) return 'intermediate';
    if (beginner.some(term => lower.includes(term))) return 'beginner';

    return 'intermediate'; // default
  }

  /**
   * Estimate overall curriculum difficulty
   */
  estimateOverallDifficulty(modules) {
    const difficulties = modules.map(m => m.difficulty);
    const advanced = difficulties.filter(d => d === 'advanced').length;
    const intermediate = difficulties.filter(d => d === 'intermediate').length;

    if (advanced > modules.length / 2) return 'advanced';
    if (intermediate > modules.length / 2) return 'intermediate';
    return 'beginner';
  }

  /**
   * Extract prerequisites from text
   */
  extractPrerequisites(text) {
    const prereqs = [];
    const lines = text.split('\n');

    for (const line of lines) {
      if (line.toLowerCase().includes('prerequisite') ||
          line.toLowerCase().includes('requirement') ||
          line.toLowerCase().includes('before starting')) {
        prereqs.push(line.trim());
      }
    }

    return prereqs;
  }

  /**
   * Extract title from text content
   */
  extractTitleFromText(text) {
    const lines = text.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('#') && !trimmed.startsWith('##')) {
        return trimmed.replace(/^#+\s*/, '');
      }
    }
    return 'Unknown Document';
  }

  /**
   * Extract author from text content
   */
  extractAuthorFromText(text) {
    const authorPatterns = [
      /author:\s*(.+)/i,
      /by\s+(.+)/i,
      /created by\s+(.+)/i
    ];

    for (const pattern of authorPatterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return 'Unknown Author';
  }

  /**
   * Extract title from URL if not available in PDF metadata
   */
  extractTitleFromUrl(url) {
    try {
      const urlObj = new globalThis.URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop();
      return filename.replace('.pdf', '').replace(/[-_]/g, ' ');
    } catch {
      return 'Unknown PDF';
    }
  }

  /**
   * Count words in text
   */
  countWords(text) {
    if (!text || typeof text !== 'string') return 0;
    return text
      .trim()
      .split(/\s+/)
      .filter(word => word.length > 0).length;
  }

  /**
   * Validate PDF URL
   */
  validateUrl(url) {
    try {
      // eslint-disable-next-line no-new
      new globalThis.URL(url);
      return this.canHandle(url);
    } catch {
      return false;
    }
  }

  /**
   * Get estimated processing time based on file size
   */
  getEstimatedProcessingTime(url, fileSize = null) {
    // PDF extraction time varies by file size
    // Small PDFs: 5-15 seconds
    // Large PDFs: 30-120 seconds
    if (fileSize) {
      const sizeMB = fileSize / (1024 * 1024);
      if (sizeMB < 1) {
        return { min: 5, max: 15, unit: 'seconds' };
      } else if (sizeMB < 10) {
        return { min: 15, max: 60, unit: 'seconds' };
      } else {
        return { min: 60, max: 180, unit: 'seconds' };
      }
    }

    return { min: 10, max: 60, unit: 'seconds' };
  }

  /**
   * Check if PDF is accessible
   */
  async checkAccessibility(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return {
        accessible: response.ok,
        status: response.status,
        contentType: response.headers.get('content-type'),
        contentLength: response.headers.get('content-length'),
      };
    } catch (error) {
      return {
        accessible: false,
        error: error.message,
      };
    }
  }
}
