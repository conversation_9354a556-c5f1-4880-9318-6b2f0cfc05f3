#!/usr/bin/env python3

"""
Working yt-dlp Extractor for ICT Videos
Tests audio extraction and basic info gathering
"""

import subprocess
import json
import os
import sys
from pathlib import Path

def test_yt_dlp_extraction(url):
    """Test yt-dlp extraction capabilities"""
    
    print("🎯 TESTING YT-DLP EXTRACTION")
    print("=" * 40)
    print(f"URL: {url}")
    print()
    
    # Step 1: Get video info without downloading
    print("📊 STEP 1: Getting video information...")
    try:
        cmd = [
            'python', '-m', 'yt_dlp',
            '--dump-json',
            '--no-download',
            url
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            video_info = json.loads(result.stdout)
            
            print("✅ SUCCESS! Video info extracted:")
            print(f"  📝 Title: {video_info.get('title', 'N/A')}")
            print(f"  👤 Uploader: {video_info.get('uploader', 'N/A')}")
            print(f"  ⏱️  Duration: {video_info.get('duration', 0)} seconds")
            print(f"  👀 Views: {video_info.get('view_count', 0):,}")
            print(f"  👍 Likes: {video_info.get('like_count', 0):,}")
            print(f"  📅 Upload Date: {video_info.get('upload_date', 'N/A')}")
            print(f"  🎥 Format Count: {len(video_info.get('formats', []))}")
            print()
            
            # Check for subtitles
            subtitles = video_info.get('subtitles', {})
            auto_captions = video_info.get('automatic_captions', {})
            
            print("📝 SUBTITLE AVAILABILITY:")
            if subtitles:
                print(f"  ✅ Manual subtitles: {list(subtitles.keys())}")
            else:
                print("  ❌ No manual subtitles")
                
            if auto_captions:
                print(f"  ✅ Auto captions: {list(auto_captions.keys())}")
            else:
                print("  ❌ No auto captions")
            print()
            
        else:
            print(f"❌ Failed to get video info: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting video info: {e}")
        return False
    
    # Step 2: Test audio extraction (small sample)
    print("🎵 STEP 2: Testing audio extraction...")
    try:
        # Create temp directory
        temp_dir = Path("temp_audio")
        temp_dir.mkdir(exist_ok=True)
        
        # Extract just first 30 seconds for testing
        output_path = temp_dir / "test_audio.mp3"
        
        cmd = [
            'python', '-m', 'yt_dlp',
            '-x',  # Extract audio only
            '--audio-format', 'mp3',
            '--audio-quality', '5',  # Medium quality for testing
            '--postprocessor-args', '-t 30',  # First 30 seconds only
            '-o', str(output_path),
            url
        ]
        
        print("  🔄 Extracting first 30 seconds of audio...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            # Check if file was created
            audio_files = list(temp_dir.glob("*.mp3"))
            if audio_files:
                audio_file = audio_files[0]
                file_size = audio_file.stat().st_size
                print(f"  ✅ SUCCESS! Audio extracted:")
                print(f"    📁 File: {audio_file.name}")
                print(f"    📏 Size: {file_size:,} bytes")
                print(f"    🎵 Format: MP3")
                
                # Clean up
                audio_file.unlink()
                temp_dir.rmdir()
                
                return True
            else:
                print("  ❌ No audio file created")
                return False
        else:
            print(f"  ❌ Audio extraction failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error during audio extraction: {e}")
        return False

def test_subtitle_extraction(url):
    """Test subtitle extraction if available"""
    
    print("📝 STEP 3: Testing subtitle extraction...")
    try:
        # Create temp directory
        temp_dir = Path("temp_subs")
        temp_dir.mkdir(exist_ok=True)
        
        cmd = [
            'python', '-m', 'yt_dlp',
            '--skip-download',
            '--write-subs',
            '--write-auto-subs',
            '--sub-langs', 'en',
            '--sub-format', 'vtt',
            '-o', str(temp_dir / '%(title)s.%(ext)s'),
            url
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        # Check for subtitle files
        sub_files = list(temp_dir.glob("*.vtt"))
        
        if sub_files:
            print("  ✅ SUCCESS! Subtitles extracted:")
            for sub_file in sub_files:
                file_size = sub_file.stat().st_size
                print(f"    📁 File: {sub_file.name}")
                print(f"    📏 Size: {file_size:,} bytes")
                
                # Read first few lines
                with open(sub_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:10]
                    print(f"    📄 Preview: {len(lines)} lines")
                
                # Clean up
                sub_file.unlink()
            
            temp_dir.rmdir()
            return True
        else:
            print("  ❌ No subtitle files created")
            # Clean up empty directory
            try:
                temp_dir.rmdir()
            except:
                pass
            return False
            
    except Exception as e:
        print(f"❌ Error during subtitle extraction: {e}")
        return False

def main():
    """Main test function"""
    
    if len(sys.argv) > 1:
        test_url = sys.argv[1]
    else:
        test_url = "https://www.youtube.com/watch?v=tmeCWULSTHc"
    
    print("🚀 YT-DLP EXTRACTION TEST")
    print("=" * 50)
    print(f"Testing with: {test_url}")
    print()
    
    # Test basic extraction
    info_success = test_yt_dlp_extraction(test_url)
    
    # Test subtitle extraction
    subtitle_success = test_subtitle_extraction(test_url)
    
    print()
    print("📊 FINAL RESULTS:")
    print("=" * 20)
    print(f"✅ Video Info: {'SUCCESS' if info_success else 'FAILED'}")
    print(f"🎵 Audio Extract: {'SUCCESS' if info_success else 'FAILED'}")
    print(f"📝 Subtitles: {'SUCCESS' if subtitle_success else 'FAILED'}")
    
    if info_success:
        print()
        print("🎯 NEXT STEPS:")
        print("1. ✅ yt-dlp is working - can extract audio reliably")
        if subtitle_success:
            print("2. ✅ Subtitles available - no need for Whisper API")
        else:
            print("2. ❌ No subtitles - Whisper API needed for transcription")
        print("3. 🚀 Ready to build full extraction pipeline")
    else:
        print()
        print("❌ yt-dlp extraction failed - need to troubleshoot")

if __name__ == "__main__":
    main()
