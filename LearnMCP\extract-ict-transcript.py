#!/usr/bin/env python3

"""
Extract and process ICT video transcript
"""

import subprocess
import re
import json
from pathlib import Path

def extract_full_transcript(url):
    """Extract complete transcript from ICT video"""
    
    print("🎯 EXTRACTING COMPLETE ICT TRANSCRIPT")
    print("=" * 50)
    print(f"URL: {url}")
    print()
    
    # Create output directory
    output_dir = Path("ict_transcripts")
    output_dir.mkdir(exist_ok=True)
    
    try:
        yt_dlp_path = Path("../.venv/Scripts/yt-dlp.exe")
        
        cmd = [
            str(yt_dlp_path),
            '--skip-download',
            '--write-auto-subs',
            '--sub-langs', 'en',
            '--sub-format', 'vtt',
            '-o', str(output_dir / '%(title)s.%(ext)s'),
            url
        ]
        
        print("🔄 Downloading transcript...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        # Find the VTT file
        vtt_files = list(output_dir.glob("*.vtt"))
        
        if vtt_files:
            vtt_file = vtt_files[0]
            print(f"✅ SUCCESS! Transcript downloaded: {vtt_file.name}")
            print(f"📏 File size: {vtt_file.stat().st_size:,} bytes")
            
            # Process the VTT content
            transcript_data = process_vtt_file(vtt_file)
            
            # Save processed transcript
            json_file = output_dir / f"{vtt_file.stem}_processed.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(transcript_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Processed transcript saved: {json_file.name}")
            
            # Display summary
            display_transcript_summary(transcript_data)
            
            # Display sample content
            display_sample_content(transcript_data)
            
            return transcript_data
            
        else:
            print("❌ No transcript file found")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def process_vtt_file(vtt_file):
    """Process VTT file into structured data"""
    
    print("\n🔄 Processing VTT file...")
    
    with open(vtt_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse VTT content
    segments = []
    lines = content.split('\n')
    
    current_segment = None
    
    for line in lines:
        line = line.strip()
        
        # Skip empty lines and headers
        if not line or line.startswith('WEBVTT') or line.startswith('Kind:') or line.startswith('Language:'):
            continue
        
        # Check for timestamp line
        timestamp_match = re.match(r'(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})', line)
        
        if timestamp_match:
            # Save previous segment
            if current_segment and current_segment['text'].strip():
                segments.append(current_segment)
            
            # Start new segment
            start_time = timestamp_to_seconds(timestamp_match.group(1))
            end_time = timestamp_to_seconds(timestamp_match.group(2))
            
            current_segment = {
                'start': start_time,
                'end': end_time,
                'duration': end_time - start_time,
                'text': ''
            }
        
        elif current_segment is not None:
            # This is text content
            # Clean up VTT formatting
            clean_text = clean_vtt_text(line)
            if clean_text:
                if current_segment['text']:
                    current_segment['text'] += ' ' + clean_text
                else:
                    current_segment['text'] = clean_text
    
    # Don't forget the last segment
    if current_segment and current_segment['text'].strip():
        segments.append(current_segment)
    
    # Create full text
    full_text = ' '.join([seg['text'] for seg in segments])
    
    # Analyze content
    analysis = analyze_trading_content(full_text, segments)
    
    return {
        'metadata': {
            'total_segments': len(segments),
            'total_duration': max([seg['end'] for seg in segments]) if segments else 0,
            'word_count': len(full_text.split()),
            'character_count': len(full_text)
        },
        'segments': segments,
        'full_text': full_text,
        'analysis': analysis
    }

def timestamp_to_seconds(timestamp):
    """Convert VTT timestamp to seconds"""
    parts = timestamp.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    return hours * 3600 + minutes * 60 + seconds

def clean_vtt_text(text):
    """Clean VTT formatting from text"""
    # Remove VTT tags like <00:00:13.200><c> and </c>
    text = re.sub(r'<[^>]+>', '', text)
    # Remove extra whitespace
    text = ' '.join(text.split())
    return text

def analyze_trading_content(full_text, segments):
    """Analyze trading-specific content"""
    
    # Trading terms to look for
    trading_terms = {
        'support': 0, 'resistance': 0, 'trend': 0, 'breakout': 0,
        'entry': 0, 'exit': 0, 'stop': 0, 'profit': 0,
        'bullish': 0, 'bearish': 0, 'chart': 0, 'candle': 0,
        'pattern': 0, 'strategy': 0, 'market': 0, 'price': 0,
        'analysis': 0, 'fibonacci': 0, 'pivot': 0, 'volume': 0,
        'momentum': 0, 'reversal': 0, 'liquidity': 0, 'institutional': 0
    }
    
    # Count trading terms
    lower_text = full_text.lower()
    for term in trading_terms:
        trading_terms[term] = len(re.findall(r'\b' + term + r'\b', lower_text))
    
    # Find key educational moments
    educational_keywords = ['important', 'key', 'remember', 'note', 'concept', 'understand', 'learn']
    key_moments = []
    
    for segment in segments:
        text_lower = segment['text'].lower()
        for keyword in educational_keywords:
            if keyword in text_lower:
                key_moments.append({
                    'time': segment['start'],
                    'timestamp': f"{int(segment['start']//60):02d}:{int(segment['start']%60):02d}",
                    'keyword': keyword,
                    'text': segment['text'][:100] + '...' if len(segment['text']) > 100 else segment['text']
                })
    
    # Sort key moments by frequency
    trading_terms_sorted = sorted(trading_terms.items(), key=lambda x: x[1], reverse=True)
    
    return {
        'trading_terms': dict(trading_terms_sorted[:10]),  # Top 10
        'total_trading_terms': sum(trading_terms.values()),
        'key_moments': key_moments[:15],  # Top 15 educational moments
        'content_density': 'High' if sum(trading_terms.values()) > 50 else 'Medium' if sum(trading_terms.values()) > 20 else 'Low'
    }

def display_transcript_summary(data):
    """Display transcript summary"""
    
    print("\n📊 TRANSCRIPT SUMMARY")
    print("=" * 30)
    
    meta = data['metadata']
    analysis = data['analysis']
    
    print(f"⏱️  Total Duration: {int(meta['total_duration']//60):02d}:{int(meta['total_duration']%60):02d}")
    print(f"📝 Total Segments: {meta['total_segments']:,}")
    print(f"🔤 Word Count: {meta['word_count']:,}")
    print(f"📄 Character Count: {meta['character_count']:,}")
    print(f"🎯 Trading Terms: {analysis['total_trading_terms']} occurrences")
    print(f"📚 Content Density: {analysis['content_density']}")
    print(f"🔑 Key Moments: {len(analysis['key_moments'])}")
    
    print(f"\n🏆 TOP TRADING CONCEPTS:")
    for term, count in list(analysis['trading_terms'].items())[:5]:
        if count > 0:
            print(f"  • {term.title()}: {count} times")

def display_sample_content(data):
    """Display sample transcript content"""
    
    print(f"\n📄 SAMPLE TRANSCRIPT CONTENT")
    print("=" * 40)
    
    # Show first 3 segments
    for i, segment in enumerate(data['segments'][:3]):
        timestamp = f"{int(segment['start']//60):02d}:{int(segment['start']%60):02d}"
        print(f"[{timestamp}] {segment['text']}")
    
    print("\n🔑 KEY EDUCATIONAL MOMENTS:")
    for moment in data['analysis']['key_moments'][:5]:
        print(f"[{moment['timestamp']}] {moment['keyword'].upper()}: {moment['text']}")

def main():
    """Main function"""
    
    url = "https://www.youtube.com/watch?v=tmeCWULSTHc"
    
    transcript_data = extract_full_transcript(url)
    
    if transcript_data:
        print(f"\n🎉 SUCCESS! Complete ICT transcript extracted and analyzed!")
        print(f"💡 This gives us everything needed for learning path creation!")
    else:
        print(f"\n❌ Failed to extract transcript")

if __name__ == "__main__":
    main()
