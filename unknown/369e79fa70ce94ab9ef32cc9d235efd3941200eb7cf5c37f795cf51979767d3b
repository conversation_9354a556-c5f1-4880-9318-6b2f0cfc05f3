name: Tests on CI

on:
  pull_request:
  push:
    branches:
      - main

concurrency:
  group: ci-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  cpu:
    name: Tests on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    if: "!contains(github.event.head_commit.message, 'ci skip')"
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.11"]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: "${{ matrix.python-version }}"
      - uses: actions/setup-go@v5
        with:
          go-version: '>=1.17.0'
      # Add Go bin directory to PATH for this workflow
      # GITHUB_PATH is a special file that GitHub Actions uses to modify PATH
      # Writing to this file adds the directory to the PATH for subsequent steps
      - name: Install gopls
        shell: bash
        run: go install golang.org/x/tools/gopls@latest
      - name: Install uv
        shell: bash
        run: curl -LsSf https://astral.sh/uv/install.sh | sh
      - name: Cache uv virtualenv
        id: cache-uv
        uses: actions/cache@v3
        with:
          path: .venv
          key: uv-venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('uv.lock') }}
      - name: Create virtual environment
        shell: bash
        run: |
          if [ ! -d ".venv" ]; then
            uv venv
          fi
      - name: Install dependencies
        shell: bash
        run: uv pip install -e ".[dev]"
      - name: Test with pytest
        shell: bash
        run: uv run poe test