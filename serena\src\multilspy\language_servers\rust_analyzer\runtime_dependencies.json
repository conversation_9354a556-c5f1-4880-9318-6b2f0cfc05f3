{"_description": "Used to download the runtime dependencies for running RustAnalyzer. Obtained from https://github.com/rust-lang/rust-analyzer/releases", "runtimeDependencies": [{"id": "RustAnalyzer", "description": "RustAnalyzer for Linux (x64)", "url": "https://github.com/rust-lang/rust-analyzer/releases/download/2023-10-09/rust-analyzer-aarch64-apple-darwin.gz", "platformId": "osx-arm64", "archiveType": "gz", "binaryName": "rust_analyzer"}, {"id": "RustAnalyzer", "description": "RustAnalyzer for Linux (x64)", "url": "https://github.com/rust-lang/rust-analyzer/releases/download/2023-10-09/rust-analyzer-x86_64-unknown-linux-gnu.gz", "platformId": "linux-x64", "archiveType": "gz", "binaryName": "rust_analyzer"}, {"id": "RustAnalyzer", "description": "RustAnalyzer for Windows (x64)", "url": "https://github.com/rust-lang/rust-analyzer/releases/download/2023-10-09/rust-analyzer-x86_64-pc-windows-msvc.zip", "platformId": "win-x64", "archiveType": "zip", "binaryName": "rust-analyzer.exe"}]}