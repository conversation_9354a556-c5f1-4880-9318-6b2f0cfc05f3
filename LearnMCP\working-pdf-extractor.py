#!/usr/bin/env python3

"""
Working PDF Extractor using PyPDF2
"""

import sys
import os
import json
import re
from pathlib import Path

def extract_pdf_content(pdf_path):
    """Extract content from PDF using PyPDF2"""
    
    if not os.path.exists(pdf_path):
        print(f"Error: File not found: {pdf_path}")
        return None
    
    print(f"PDF CONTENT EXTRACTOR")
    print("=" * 40)
    print(f"File: {pdf_path}")
    print()
    
    try:
        import PyPDF2
        print("Using PyPDF2 for extraction...")
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Extract metadata
            metadata = pdf_reader.metadata or {}
            
            print(f"SUCCESS! PDF opened")
            print(f"Pages: {len(pdf_reader.pages)}")
            print(f"Title: {metadata.get('/Title', 'Unknown')}")
            print(f"Author: {metadata.get('/Author', 'Unknown')}")
            print()
            
            # Extract text from all pages
            pages_content = []
            total_text = ""
            
            print("Extracting text from pages...")
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        clean_text = page_text.strip()
                        pages_content.append({
                            'page': page_num + 1,
                            'text': clean_text,
                            'word_count': len(clean_text.split()),
                            'char_count': len(clean_text)
                        })
                        total_text += clean_text + "\n"
                        print(f"  Page {page_num + 1}: {len(clean_text.split())} words")
                    else:
                        print(f"  Page {page_num + 1}: No text found")
                except Exception as e:
                    print(f"  Page {page_num + 1}: Error - {e}")
            
            if not total_text.strip():
                print("WARNING: No text content extracted from PDF")
                return None
            
            # Analyze content
            analysis = analyze_content(total_text)
            
            result = {
                'file_info': {
                    'path': pdf_path,
                    'filename': os.path.basename(pdf_path),
                    'size_bytes': os.path.getsize(pdf_path)
                },
                'metadata': {
                    'title': metadata.get('/Title', 'Unknown'),
                    'author': metadata.get('/Author', 'Unknown'),
                    'subject': metadata.get('/Subject', 'Unknown'),
                    'creator': metadata.get('/Creator', 'Unknown'),
                    'pages': len(pdf_reader.pages)
                },
                'content': {
                    'pages': pages_content,
                    'full_text': total_text,
                    'total_words': len(total_text.split()),
                    'total_characters': len(total_text),
                    'pages_with_text': len(pages_content)
                },
                'analysis': analysis
            }
            
            return result
            
    except ImportError:
        print("ERROR: PyPDF2 not installed. Run: pip install PyPDF2")
        return None
    except Exception as e:
        print(f"ERROR: Failed to extract PDF: {e}")
        return None

def analyze_content(text):
    """Analyze the extracted content"""
    
    if not text or not text.strip():
        return {'error': 'No text to analyze'}
    
    # Basic text analysis
    words = text.split()
    sentences = re.split(r'[.!?]+', text)
    paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
    
    # Look for educational patterns
    educational_indicators = {
        'headings': len(re.findall(r'^[A-Z][A-Z\s]+$', text, re.MULTILINE)),
        'numbered_lists': len(re.findall(r'^\d+\.', text, re.MULTILINE)),
        'bullet_points': len(re.findall(r'^[-*•]', text, re.MULTILINE)),
        'questions': len(re.findall(r'\?', text)),
        'definitions': len(re.findall(r':\s*[A-Z]', text)),
        'examples': len(re.findall(r'example|for instance|such as', text, re.IGNORECASE)),
        'references': len(re.findall(r'see|refer|chapter|section|page', text, re.IGNORECASE))
    }
    
    # Educational keywords
    educational_terms = [
        'learn', 'understand', 'concept', 'principle', 'theory',
        'practice', 'exercise', 'example', 'definition', 'explanation',
        'objective', 'goal', 'skill', 'knowledge', 'method', 'study'
    ]
    
    term_counts = {}
    lower_text = text.lower()
    for term in educational_terms:
        count = len(re.findall(r'\b' + term + r'\b', lower_text))
        if count > 0:
            term_counts[term] = count
    
    # Content classification
    content_type = classify_content(text)
    difficulty = estimate_difficulty(text)
    
    return {
        'basic_stats': {
            'word_count': len(words),
            'sentence_count': len([s for s in sentences if s.strip()]),
            'paragraph_count': len(paragraphs),
            'avg_words_per_sentence': len(words) / max(len(sentences), 1),
            'avg_sentence_length': sum(len(s) for s in sentences) / max(len(sentences), 1)
        },
        'educational_indicators': educational_indicators,
        'educational_terms': term_counts,
        'total_educational_score': sum(term_counts.values()) + sum(educational_indicators.values()),
        'content_classification': {
            'type': content_type,
            'difficulty': difficulty,
            'is_educational': sum(term_counts.values()) > 5
        }
    }

def classify_content(text):
    """Classify the type of content"""
    lower_text = text.lower()
    
    # Check for different content types
    if any(term in lower_text for term in ['trading', 'market', 'financial', 'investment', 'forex', 'stocks']):
        return 'Financial/Trading'
    elif any(term in lower_text for term in ['programming', 'code', 'software', 'algorithm', 'development']):
        return 'Technical/Programming'
    elif any(term in lower_text for term in ['research', 'study', 'analysis', 'methodology', 'academic']):
        return 'Academic/Research'
    elif any(term in lower_text for term in ['manual', 'guide', 'instructions', 'how to', 'tutorial']):
        return 'Instructional Guide'
    elif any(term in lower_text for term in ['business', 'management', 'strategy', 'corporate']):
        return 'Business/Management'
    else:
        return 'General Document'

def estimate_difficulty(text):
    """Estimate content difficulty"""
    words = text.split()
    if not words:
        return 'Unknown'
    
    # Count complex words (more than 6 characters)
    complex_words = [w for w in words if len(w) > 6]
    complexity_ratio = len(complex_words) / len(words)
    
    # Count technical terms (words with numbers, capitals, etc.)
    technical_words = [w for w in words if re.search(r'[A-Z]{2,}|\d+', w)]
    technical_ratio = len(technical_words) / len(words)
    
    total_complexity = complexity_ratio + technical_ratio
    
    if total_complexity > 0.4:
        return 'Advanced'
    elif total_complexity > 0.25:
        return 'Intermediate'
    else:
        return 'Beginner'

def display_results(result):
    """Display extraction results"""
    
    print("\nEXTRACTION RESULTS")
    print("=" * 30)
    
    # File info
    print(f"File: {result['file_info']['filename']}")
    print(f"Size: {result['file_info']['size_bytes']:,} bytes")
    
    # Metadata
    meta = result['metadata']
    print(f"Title: {meta['title']}")
    print(f"Author: {meta['author']}")
    print(f"Pages: {meta['pages']}")
    
    # Content stats
    content = result['content']
    print(f"Pages with text: {content['pages_with_text']}")
    print(f"Total words: {content['total_words']:,}")
    print(f"Total characters: {content['total_characters']:,}")
    
    # Analysis
    analysis = result['analysis']
    if 'error' not in analysis:
        print(f"\nCONTENT ANALYSIS")
        print(f"Content type: {analysis['content_classification']['type']}")
        print(f"Difficulty: {analysis['content_classification']['difficulty']}")
        print(f"Educational score: {analysis['total_educational_score']}")
        print(f"Is educational: {analysis['content_classification']['is_educational']}")
        
        # Show top educational terms
        if analysis['educational_terms']:
            print(f"\nTop educational terms:")
            sorted_terms = sorted(analysis['educational_terms'].items(), key=lambda x: x[1], reverse=True)
            for term, count in sorted_terms[:5]:
                print(f"  {term}: {count} times")
    
    # Sample content
    print(f"\nSAMPLE CONTENT (first 500 characters):")
    print("-" * 40)
    print(content['full_text'][:500] + "...")

def main():
    """Main function"""
    
    if len(sys.argv) < 2:
        print("Usage: python working-pdf-extractor.py <pdf_file>")
        print("Example: python working-pdf-extractor.py document.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    # Extract content
    result = extract_pdf_content(pdf_path)
    
    if result:
        # Display results
        display_results(result)
        
        # Save to JSON
        output_file = Path(pdf_path).stem + "_extracted.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\nResults saved to: {output_file}")
        print("SUCCESS! PDF extraction complete.")
    else:
        print("FAILED: Could not extract PDF content.")

if __name__ == "__main__":
    main()
