name: Upload Python Package

on:
  release:
    types: [created]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v1
        with:
          python-version: 3.11
          
      
      - name: Install build and twine
        run: pip install build twine
      - name: Build and publish
        env:
          PYPI_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          if [ -z "${PYPI_TOKEN}" ]; then echo "Set the PYPI_TOKEN variable in your repository secrets"; exit 1; fi
          python -m build
          python -m twine upload dist/* --username __token__ --password $PYPI_TOKEN
      
