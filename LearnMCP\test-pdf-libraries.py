#!/usr/bin/env python3

"""
Test PDF extraction libraries availability
"""

def test_pdf_libraries():
    """Test which PDF libraries are available"""
    
    print("🔍 TESTING PDF LIBRARY AVAILABILITY")
    print("=" * 40)
    
    libraries = {
        'PyPDF2': 'pip install PyPDF2',
        'pdfplumber': 'pip install pdfplumber', 
        'PyMuPDF': 'pip install PyMuPDF',
        'pdfminer': 'pip install pdfminer.six'
    }
    
    available = []
    missing = []
    
    # Test PyPDF2
    try:
        import PyPDF2
        print("✅ PyPDF2: Available")
        available.append('PyPDF2')
    except ImportError:
        print("❌ PyPDF2: Not installed")
        missing.append(('PyPDF2', libraries['PyPDF2']))
    
    # Test pdfplumber
    try:
        import pdfplumber
        print("✅ pdfplumber: Available")
        available.append('pdfplumber')
    except ImportError:
        print("❌ pdfplumber: Not installed")
        missing.append(('pdfplumber', libraries['pdfplumber']))
    
    # Test PyMuPDF
    try:
        import fitz  # PyMuPDF
        print("✅ PyMuPDF: Available")
        available.append('PyMuPDF')
    except ImportError:
        print("❌ PyMuPDF: Not installed")
        missing.append(('PyMuPDF', libraries['PyMuPDF']))
    
    # Test pdfminer
    try:
        from pdfminer.high_level import extract_text
        print("✅ pdfminer: Available")
        available.append('pdfminer')
    except ImportError:
        print("❌ pdfminer: Not installed")
        missing.append(('pdfminer', libraries['pdfminer']))
    
    print(f"\n📊 SUMMARY:")
    print(f"Available: {len(available)}/4 libraries")
    print(f"Missing: {len(missing)}/4 libraries")
    
    if available:
        print(f"\n✅ Can proceed with: {', '.join(available)}")
    
    if missing:
        print(f"\n📦 To install missing libraries:")
        for name, command in missing:
            print(f"  {command}")
    
    return available, missing

def create_simple_extractor():
    """Create a simple extractor with available libraries"""
    
    available, missing = test_pdf_libraries()
    
    if not available:
        print("\n❌ No PDF libraries available. Please install at least one.")
        return
    
    print(f"\n🚀 Creating simple extractor with available libraries...")
    
    extractor_code = '''#!/usr/bin/env python3

"""
Simple PDF Extractor using available libraries
"""

import sys
import os

def extract_pdf_simple(pdf_path):
    """Extract PDF using available method"""
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return None
    
    print(f"🎯 Extracting: {pdf_path}")
    
'''
    
    # Add extraction methods based on available libraries
    if 'PyPDF2' in available:
        extractor_code += '''
    # Try PyPDF2 first
    try:
        import PyPDF2
        print("🔄 Using PyPDF2...")
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            text_content = []
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text.strip():
                    text_content.append(page_text.strip())
            
            full_text = '\\n'.join(text_content)
            
            print(f"✅ SUCCESS with PyPDF2!")
            print(f"📄 Pages: {len(pdf_reader.pages)}")
            print(f"🔤 Words: {len(full_text.split()):,}")
            print(f"📝 Characters: {len(full_text):,}")
            print(f"\\n📄 SAMPLE CONTENT (first 500 chars):")
            print(full_text[:500] + "...")
            
            return full_text
            
    except Exception as e:
        print(f"❌ PyPDF2 failed: {e}")
'''
    
    if 'pdfplumber' in available:
        extractor_code += '''
    # Try pdfplumber
    try:
        import pdfplumber
        print("🔄 Using pdfplumber...")
        
        with pdfplumber.open(pdf_path) as pdf:
            text_content = []
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text and page_text.strip():
                    text_content.append(page_text.strip())
            
            full_text = '\\n'.join(text_content)
            
            print(f"✅ SUCCESS with pdfplumber!")
            print(f"📄 Pages: {len(pdf.pages)}")
            print(f"🔤 Words: {len(full_text.split()):,}")
            print(f"📝 Characters: {len(full_text):,}")
            print(f"\\n📄 SAMPLE CONTENT (first 500 chars):")
            print(full_text[:500] + "...")
            
            return full_text
            
    except Exception as e:
        print(f"❌ pdfplumber failed: {e}")
'''
    
    extractor_code += '''
    print("❌ All extraction methods failed")
    return None

def main():
    if len(sys.argv) < 2:
        print("Usage: python simple_pdf_extractor.py <pdf_file>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    result = extract_pdf_simple(pdf_path)
    
    if result:
        print("\\n🎉 PDF extraction successful!")
    else:
        print("\\n❌ PDF extraction failed")

if __name__ == "__main__":
    main()
'''
    
    # Save the simple extractor
    with open('simple_pdf_extractor.py', 'w') as f:
        f.write(extractor_code)
    
    print("✅ Created: simple_pdf_extractor.py")
    print("📝 Usage: python simple_pdf_extractor.py <pdf_file>")

if __name__ == "__main__":
    test_pdf_libraries()
    print()
    create_simple_extractor()
