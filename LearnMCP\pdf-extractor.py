#!/usr/bin/env python3

"""
PDF Content Extractor for Educational Materials
Reliable extraction with multiple fallback strategies
"""

import os
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Optional

def extract_pdf_content(pdf_path: str) -> Dict:
    """
    Extract comprehensive content from PDF with multiple fallback methods
    """
    
    print(f"🎯 EXTRACTING PDF CONTENT")
    print("=" * 40)
    print(f"File: {pdf_path}")
    print()
    
    results = {
        'file_path': pdf_path,
        'extraction_methods': [],
        'final_result': None,
        'extraction_time': None
    }
    
    # Method 1: PyPDF2 (most common)
    try:
        result1 = extract_with_pypdf2(pdf_path)
        results['extraction_methods'].append(result1)
        if result1['success']:
            results['final_result'] = result1
            print("✅ Extraction successful with PyPDF2")
            return results
    except Exception as e:
        results['extraction_methods'].append({
            'method': 'pypdf2',
            'success': False,
            'error': str(e)
        })
    
    # Method 2: pdfplumber (better for complex layouts)
    try:
        result2 = extract_with_pdfplumber(pdf_path)
        results['extraction_methods'].append(result2)
        if result2['success']:
            results['final_result'] = result2
            print("✅ Extraction successful with pdfplumber")
            return results
    except Exception as e:
        results['extraction_methods'].append({
            'method': 'pdfplumber',
            'success': False,
            'error': str(e)
        })
    
    # Method 3: pymupdf (fitz) - handles complex PDFs
    try:
        result3 = extract_with_pymupdf(pdf_path)
        results['extraction_methods'].append(result3)
        if result3['success']:
            results['final_result'] = result3
            print("✅ Extraction successful with PyMuPDF")
            return results
    except Exception as e:
        results['extraction_methods'].append({
            'method': 'pymupdf',
            'success': False,
            'error': str(e)
        })
    
    # Method 4: pdfminer (most thorough)
    try:
        result4 = extract_with_pdfminer(pdf_path)
        results['extraction_methods'].append(result4)
        if result4['success']:
            results['final_result'] = result4
            print("✅ Extraction successful with pdfminer")
            return results
    except Exception as e:
        results['extraction_methods'].append({
            'method': 'pdfminer',
            'success': False,
            'error': str(e)
        })
    
    print("❌ All extraction methods failed")
    return results

def extract_with_pypdf2(pdf_path: str) -> Dict:
    """Extract using PyPDF2"""
    try:
        import PyPDF2
        
        print("🔄 Trying PyPDF2...")
        
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # Extract metadata
            metadata = pdf_reader.metadata or {}
            
            # Extract text from all pages
            text_content = []
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text.strip():
                    text_content.append({
                        'page': page_num + 1,
                        'text': page_text.strip()
                    })
            
            full_text = '\n'.join([page['text'] for page in text_content])
            
            return {
                'method': 'pypdf2',
                'success': True,
                'confidence': 0.8,
                'data': {
                    'metadata': {
                        'title': metadata.get('/Title', 'Unknown'),
                        'author': metadata.get('/Author', 'Unknown'),
                        'subject': metadata.get('/Subject', 'Unknown'),
                        'creator': metadata.get('/Creator', 'Unknown'),
                        'pages': len(pdf_reader.pages)
                    },
                    'pages': text_content,
                    'full_text': full_text,
                    'word_count': len(full_text.split()),
                    'character_count': len(full_text)
                }
            }
    except Exception as e:
        return {'method': 'pypdf2', 'success': False, 'error': str(e)}

def extract_with_pdfplumber(pdf_path: str) -> Dict:
    """Extract using pdfplumber (better for tables and layout)"""
    try:
        import pdfplumber
        
        print("🔄 Trying pdfplumber...")
        
        with pdfplumber.open(pdf_path) as pdf:
            # Extract metadata
            metadata = pdf.metadata or {}
            
            # Extract text and tables from all pages
            pages_content = []
            tables_found = []
            
            for page_num, page in enumerate(pdf.pages):
                page_text = page.extract_text()
                
                # Extract tables
                tables = page.extract_tables()
                if tables:
                    tables_found.extend([{
                        'page': page_num + 1,
                        'table_index': i,
                        'table': table
                    } for i, table in enumerate(tables)])
                
                if page_text and page_text.strip():
                    pages_content.append({
                        'page': page_num + 1,
                        'text': page_text.strip(),
                        'has_tables': len(tables) > 0
                    })
            
            full_text = '\n'.join([page['text'] for page in pages_content])
            
            return {
                'method': 'pdfplumber',
                'success': True,
                'confidence': 0.9,
                'data': {
                    'metadata': {
                        'title': metadata.get('Title', 'Unknown'),
                        'author': metadata.get('Author', 'Unknown'),
                        'subject': metadata.get('Subject', 'Unknown'),
                        'creator': metadata.get('Creator', 'Unknown'),
                        'pages': len(pdf.pages)
                    },
                    'pages': pages_content,
                    'tables': tables_found,
                    'full_text': full_text,
                    'word_count': len(full_text.split()),
                    'character_count': len(full_text),
                    'table_count': len(tables_found)
                }
            }
    except Exception as e:
        return {'method': 'pdfplumber', 'success': False, 'error': str(e)}

def extract_with_pymupdf(pdf_path: str) -> Dict:
    """Extract using PyMuPDF (fitz) - good for complex layouts"""
    try:
        import fitz  # PyMuPDF
        
        print("🔄 Trying PyMuPDF...")
        
        doc = fitz.open(pdf_path)
        
        # Extract metadata
        metadata = doc.metadata
        
        # Extract text from all pages
        pages_content = []
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            
            if page_text.strip():
                pages_content.append({
                    'page': page_num + 1,
                    'text': page_text.strip()
                })
        
        doc.close()
        
        full_text = '\n'.join([page['text'] for page in pages_content])
        
        return {
            'method': 'pymupdf',
            'success': True,
            'confidence': 0.85,
            'data': {
                'metadata': {
                    'title': metadata.get('title', 'Unknown'),
                    'author': metadata.get('author', 'Unknown'),
                    'subject': metadata.get('subject', 'Unknown'),
                    'creator': metadata.get('creator', 'Unknown'),
                    'pages': len(doc)
                },
                'pages': pages_content,
                'full_text': full_text,
                'word_count': len(full_text.split()),
                'character_count': len(full_text)
            }
        }
    except Exception as e:
        return {'method': 'pymupdf', 'success': False, 'error': str(e)}

def extract_with_pdfminer(pdf_path: str) -> Dict:
    """Extract using pdfminer - most thorough but slower"""
    try:
        from pdfminer.high_level import extract_text
        from pdfminer.pdfpage import PDFPage
        from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
        from pdfminer.converter import TextConverter
        from pdfminer.layout import LAParams
        from io import StringIO
        
        print("🔄 Trying pdfminer...")
        
        # Extract full text
        full_text = extract_text(pdf_path)
        
        # Extract page by page for more detailed analysis
        pages_content = []
        with open(pdf_path, 'rb') as file:
            resource_manager = PDFResourceManager()
            
            for page_num, page in enumerate(PDFPage.get_pages(file)):
                output_string = StringIO()
                device = TextConverter(resource_manager, output_string, laparams=LAParams())
                interpreter = PDFPageInterpreter(resource_manager, device)
                interpreter.process_page(page)
                
                page_text = output_string.getvalue()
                if page_text.strip():
                    pages_content.append({
                        'page': page_num + 1,
                        'text': page_text.strip()
                    })
                
                device.close()
                output_string.close()
        
        return {
            'method': 'pdfminer',
            'success': True,
            'confidence': 0.95,
            'data': {
                'metadata': {
                    'title': 'Unknown',
                    'author': 'Unknown',
                    'subject': 'Unknown',
                    'creator': 'Unknown',
                    'pages': len(pages_content)
                },
                'pages': pages_content,
                'full_text': full_text,
                'word_count': len(full_text.split()),
                'character_count': len(full_text)
            }
        }
    except Exception as e:
        return {'method': 'pdfminer', 'success': False, 'error': str(e)}

def analyze_educational_content(text: str) -> Dict:
    """Analyze educational content structure"""
    
    # Look for educational patterns
    patterns = {
        'headings': len(re.findall(r'^[A-Z][A-Z\s]+$', text, re.MULTILINE)),
        'numbered_lists': len(re.findall(r'^\d+\.', text, re.MULTILINE)),
        'bullet_points': len(re.findall(r'^[•\-\*]', text, re.MULTILINE)),
        'questions': len(re.findall(r'\?', text)),
        'definitions': len(re.findall(r':\s*[A-Z]', text)),
        'examples': len(re.findall(r'example|for instance|such as', text, re.IGNORECASE)),
        'references': len(re.findall(r'see|refer|chapter|section', text, re.IGNORECASE))
    }
    
    # Educational keywords
    educational_terms = [
        'learn', 'understand', 'concept', 'principle', 'theory',
        'practice', 'exercise', 'example', 'definition', 'explanation',
        'objective', 'goal', 'skill', 'knowledge', 'method'
    ]
    
    term_counts = {}
    lower_text = text.lower()
    for term in educational_terms:
        term_counts[term] = len(re.findall(r'\b' + term + r'\b', lower_text))
    
    return {
        'structure_patterns': patterns,
        'educational_terms': term_counts,
        'total_educational_indicators': sum(term_counts.values()),
        'content_type': classify_content_type(text),
        'difficulty_level': estimate_difficulty(text)
    }

def classify_content_type(text: str) -> str:
    """Classify the type of educational content"""
    lower_text = text.lower()
    
    if any(term in lower_text for term in ['trading', 'market', 'financial', 'investment']):
        return 'Financial/Trading Education'
    elif any(term in lower_text for term in ['programming', 'code', 'software', 'algorithm']):
        return 'Technical/Programming'
    elif any(term in lower_text for term in ['research', 'study', 'analysis', 'methodology']):
        return 'Academic/Research'
    elif any(term in lower_text for term in ['manual', 'guide', 'instructions', 'how to']):
        return 'Instructional Guide'
    else:
        return 'General Educational'

def estimate_difficulty(text: str) -> str:
    """Estimate content difficulty level"""
    # Simple heuristic based on vocabulary complexity
    words = text.split()
    if not words:
        return 'Unknown'
    
    # Count complex words (>6 characters)
    complex_words = [w for w in words if len(w) > 6]
    complexity_ratio = len(complex_words) / len(words)
    
    if complexity_ratio > 0.3:
        return 'Advanced'
    elif complexity_ratio > 0.2:
        return 'Intermediate'
    else:
        return 'Beginner'

def main():
    """Test the PDF extractor"""
    if len(sys.argv) < 2:
        print("Usage: python pdf-extractor.py <pdf_file_path>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        sys.exit(1)
    
    # Extract content
    results = extract_pdf_content(pdf_path)
    
    if results['final_result']:
        data = results['final_result']['data']
        
        print(f"\n📊 EXTRACTION SUMMARY:")
        print(f"Method: {results['final_result']['method']}")
        print(f"Pages: {data['metadata']['pages']}")
        print(f"Words: {data['word_count']:,}")
        print(f"Characters: {data['character_count']:,}")
        
        # Analyze content
        analysis = analyze_educational_content(data['full_text'])
        
        print(f"\n🧠 CONTENT ANALYSIS:")
        print(f"Content Type: {analysis['content_type']}")
        print(f"Difficulty: {analysis['difficulty_level']}")
        print(f"Educational Indicators: {analysis['total_educational_indicators']}")
        
        print(f"\n📄 SAMPLE CONTENT (first 300 chars):")
        print(data['full_text'][:300] + "...")
        
        # Save results
        output_file = Path(pdf_path).stem + "_extracted.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                'extraction_results': results,
                'content_analysis': analysis
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Results saved to: {output_file}")
    else:
        print(f"\n❌ All extraction methods failed:")
        for attempt in results['extraction_methods']:
            print(f"  • {attempt['method']}: {attempt.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
