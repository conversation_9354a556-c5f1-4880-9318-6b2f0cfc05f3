{"id": "regression-test-1751167269970", "goal": "Test regression-proof HTA generation", "specific_interests": [], "learning_paths": [{"path_name": "general", "priority": "high"}], "context": "Validate that schema errors are handled gracefully", "constraints": {}, "existing_credentials": [], "current_habits": {}, "life_structure_preferences": {"wake_time": "08:00", "sleep_time": "23:00", "focus_duration": "25 minutes"}, "urgency_level": "medium", "success_metrics": [], "created_at": "2025-06-29T03:21:09.970Z", "knowledge_level": 1, "skill_mappings": {}, "progress": 0, "activePath": "general"}